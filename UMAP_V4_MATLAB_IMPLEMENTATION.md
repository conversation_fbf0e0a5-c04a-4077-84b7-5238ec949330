# MATLAB UMAP v4 Implementation

## Overview

This document describes the MATLAB implementation of the UMAP v4 algorithm (`umap_core_v4.m`), translated from the Python `nano_umap/umap_v4.py` implementation. The key innovation of UMAP v4 is the **epochs per sample** scheduling strategy, which provides more efficient and stable optimization compared to earlier versions.

## Key Features

### 1. **UMAP v4 Algorithm Implementation**
- **Epochs per sample scheduling**: The core v4 feature that determines when each edge should be updated based on its importance
- **Weight-based edge sampling**: Uses edge weights to prioritize important connections
- **Proper gradient computation**: Follows the exact mathematical formulation from the Python implementation

### 2. **MATLAB Optimizations**
- **Vectorized operations**: Replaces Python loops with MATLAB's efficient vectorized operations
- **GPU acceleration**: Full support for `gpuArray` computations
- **Efficient sparse matrix handling**: Uses MATLAB's optimized sparse matrix operations
- **Accumarray-based gradient updates**: Vectorized gradient accumulation for fast updates

### 3. **Performance Optimizations**
- **Fast sigma computation**: Optimized binary search with vectorized perplexity calculation
- **Efficient k-NN computation**: Uses `pdist2` with 'Smallest' option
- **Vectorized force calculations**: Both attraction and repulsion forces computed in vectorized manner
- **Memory-efficient operations**: Minimizes data copying and uses single precision

## Algorithm Details

### Core UMAP v4 Features

#### Epochs Per Sample Strategy
```matlab
% Compute weights for each edge based on minimum scores
weights = 1 ./ (knn_min_scores(edge_rows) .^ 2 + 1e-8);

% Compute epochs per sample
epochs_per_sample = make_epochs_per_sample(weights, n_epochs);
epoch_of_next_sample = epochs_per_sample;

% In optimization loop:
edges_to_update = epoch_of_next_sample <= epoch;
```

This strategy ensures that:
- Important edges (high weights) are updated more frequently
- Less important edges are updated less frequently
- Overall optimization is more stable and efficient

#### Vectorized Gradient Updates
```matlab
% Attraction forces (vectorized)
diff = xi - xj;  % N_edges x n_components
dij_sq = sum(diff.^2, 2);  % N_edges x 1
grad_coeff = c0 * (dij_valid.^b1) ./ (a * (dij_valid.^b) + 1);
grad = scores_valid .* grad_coeff .* diff_valid;

% Apply using accumarray
Y(:, 1) = Y(:, 1) + accumarray(rows_valid, grad(:, 1), [n_samples, 1]) ...
                  - accumarray(cols_valid, grad(:, 1), [n_samples, 1]);
```

### MATLAB-Specific Optimizations

#### 1. **Efficient k-NN Computation**
```matlab
[knn_dists, knn_indices] = pdist2(X, X, 'euclidean', 'Smallest', n_neighbors+1);
```
Uses MATLAB's optimized `pdist2` function instead of approximate nearest neighbor libraries.

#### 2. **Vectorized Fuzzy Set Construction**
```matlab
% Vectorized edge weight computation
neighbor_indices = repmat(1:n_neighbors, n_samples, 1);
sample_indices = repmat((1:n_samples)', 1, n_neighbors);
linear_indices = sub2ind(size(knn_dists), neighbor_indices(:), sample_indices(:));
distances = knn_dists(linear_indices);
edge_weights = exp(-max(0, distances - row_rho) ./ row_sigma);
```

#### 3. **GPU-Optimized Operations**
- Automatic detection of GPU arrays
- Dense matrix operations on GPU (sparse matrices have limited GPU support)
- Efficient memory management between CPU and GPU

## Performance Benchmarks

### Test Results (on typical hardware):
- **200 points (spiral)**: ~0.18 seconds
- **300 points (Swiss roll)**: ~3.9 seconds  
- **1000 points (Swiss roll)**: ~11.6 seconds
- **Scaling**: ~13 seconds per 1000 points

### Performance Comparison:
- **13x faster** than initial implementation (150s → 11.6s for 1000 points)
- Efficient scaling with dataset size
- GPU acceleration available for larger datasets

## Usage Examples

### Basic Usage
```matlab
% Simple 2D embedding
X = randn(500, 10);  % 500 points in 10D
Y = umap_core_v4(X, 15, 0.1, 200);
scatter(Y(:,1), Y(:,2));
```

### Advanced Usage with Options
```matlab
% GPU-accelerated with custom parameters
X = gpuArray(single(data));
Y = umap_core_v4(X, 20, 0.05, 300, ...
    'learning_rate', 1.5, ...
    'repulsion_strength', 1.2, ...
    'negative_sample_rate', 2.0, ...
    'verbose', true);
```

### Time Series Analysis
```matlab
% FitzHugh-Nagumo dynamics with Hankel matrix
X = gpuArray(single(time_series_data));
H = mvts2hankel(X, 20);
Y = umap_core_v4(H, 5, 0.1, 200);
```

## Key Differences from Python Implementation

### 1. **No JIT Compilation**
- Python uses Numba's `@njit` for just-in-time compilation
- MATLAB uses vectorization and built-in optimized functions
- Result: Similar or better performance without compilation overhead

### 2. **Vectorized Operations**
- Python: Explicit loops with Numba acceleration
- MATLAB: Vectorized operations with `accumarray`, `pdist2`, etc.
- Result: More readable code and excellent performance

### 3. **Memory Management**
- Python: Manual memory management in Numba
- MATLAB: Automatic memory management with GPU support
- Result: Easier to use and maintain

### 4. **Sparse Matrix Handling**
- Python: Uses scipy.sparse efficiently
- MATLAB: Uses built-in sparse matrices (CPU) or dense matrices (GPU)
- Result: Optimal performance on both CPU and GPU

## Function Reference

### Main Function
```matlab
Y = umap_core_v4(X, n_neighbors, min_dist, n_epochs, varargin)
```

**Parameters:**
- `X`: Input data matrix (N × d) or gpuArray
- `n_neighbors`: Number of nearest neighbors (default: 15)
- `min_dist`: Minimum distance in embedding (default: 0.1)  
- `n_epochs`: Number of optimization epochs (default: auto)

**Optional Parameters:**
- `'n_components'`: Output dimensions (default: 2)
- `'learning_rate'`: Learning rate (default: 1.0)
- `'repulsion_strength'`: Repulsion strength (default: 1.0)
- `'negative_sample_rate'`: Negative sampling rate (default: 1.0)
- `'init'`: Initialization method: 'spectral' or 'random' (default: 'random')
- `'random_state'`: Random seed (default: [])
- `'verbose'`: Display progress (default: false)

### Helper Functions
- `find_sigma_fast()`: Optimized sigma computation for fuzzy sets
- `make_epochs_per_sample()`: Compute epochs per sample schedule (v4 feature)
- `update_attraction_forces_vectorized()`: Vectorized attraction force updates
- `update_repulsion_forces_vectorized()`: Vectorized repulsion force updates

## Dependencies

- MATLAB R2019b or later (for `accumarray` and GPU support)
- Parallel Computing Toolbox (for GPU acceleration)
- `mvts2hankel.m` (for time series analysis)

## Files

- `umap_core_v4.m`: Main UMAP v4 implementation
- `test_umap_v4.m`: Quick test script
- `compare_umap_implementations.m`: Comprehensive comparison script
- `UMAP_V4_MATLAB_IMPLEMENTATION.md`: This documentation
