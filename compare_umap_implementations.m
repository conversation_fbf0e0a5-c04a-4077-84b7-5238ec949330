% Comparison between our improved umap_core and the original run_umap
close all; clear; clc;

fprintf('=== UMAP Implementation Comparison ===\n');
fprintf('Comparing our vectorized umap_core vs original run_umap\n\n');

%% Generate test data - Swiss Roll
fprintf('Generating Swiss Roll test data...\n');
n_points = 1000;
t = (3*pi/2) * (1 + 2*rand(n_points, 1));
height = 21 * rand(n_points, 1);
X_swiss = [t.*cos(t), height, t.*sin(t)] + 0.1*randn(n_points, 3);

% Common parameters for both implementations
n_neighbors = 15;
min_dist = 0.1;
n_epochs = 300;

fprintf('Configuration:\n');
fprintf('  - Data points: %d\n', n_points);
fprintf('  - Dimensions: %d -> 2\n', size(X_swiss, 2));
fprintf('  - n_neighbors: %d\n', n_neighbors);
fprintf('  - min_dist: %.1f\n', min_dist);
fprintf('  - n_epochs: %d\n\n', n_epochs);

%% Test 1: Our improved umap_core
fprintf('=== Test 1: Our Vectorized umap_core ===\n');
X_gpu = gpuArray(single(X_swiss));

tic;
Y_our = umap_core(X_gpu, n_neighbors, min_dist, n_epochs);
time_our = toc;

fprintf('Our implementation completed in %.2f seconds\n', time_our);
fprintf('Output size: %dx%d\n', size(Y_our, 1), size(Y_our, 2));

%% Test 2: Original run_umap
fprintf('\n=== Test 2: Original run_umap ===\n');

try
    tic;
    [Y_original, ~] = run_umap(X_swiss, ...
        'n_neighbors', n_neighbors, ...
        'min_dist', min_dist, ...
        'n_epochs', n_epochs, ...
        'verbose', 'none', ...
        'method', 'MEX');
    time_original = toc;
    
    fprintf('Original implementation completed in %.2f seconds\n', time_original);
    fprintf('Output size: %dx%d\n', size(Y_original, 1), size(Y_original, 2));
    
    original_available = true;
catch ME
    fprintf('Original run_umap not available: %s\n', ME.message);
    fprintf('Creating synthetic comparison data...\n');
    
    % Create a simple alternative for comparison if run_umap is not available
    Y_original = tsne(X_swiss, 'NumDimensions', 2, 'Verbose', 0);
    time_original = NaN;
    original_available = false;
end

%% Performance Comparison
fprintf('\n=== Performance Summary ===\n');
fprintf('Our implementation:      %.2f seconds\n', time_our);
if original_available
    fprintf('Original implementation: %.2f seconds\n', time_original);
    fprintf('Speed improvement:       %.1fx\n', time_original / time_our);
else
    fprintf('Original implementation: Not available\n');
end

%% Visual Comparison
figure('Position', [100, 100, 1200, 800]);

% Original data
subplot(2, 3, 1);
scatter3(X_swiss(:,1), X_swiss(:,2), X_swiss(:,3), 20, t, 'filled');
title('Original Swiss Roll Data');
xlabel('X1'); ylabel('X2'); zlabel('X3');
colorbar;
view(45, 30);

% Our implementation result
subplot(2, 3, 2);
scatter(Y_our(:,1), Y_our(:,2), 20, t, 'filled');
title(sprintf('Our umap\\_core (%.1fs)', time_our));
xlabel('UMAP 1'); ylabel('UMAP 2');
colorbar;
axis equal tight;

% Original implementation result
subplot(2, 3, 3);
scatter(Y_original(:,1), Y_original(:,2), 20, t, 'filled');
if original_available
    title(sprintf('Original run\\_umap (%.1fs)', time_original));
else
    title('t-SNE (for comparison)');
end
xlabel('Dimension 1'); ylabel('Dimension 2');
colorbar;
axis equal tight;

%% Test with FitzHugh-Nagumo data (like in demo)
fprintf('\n=== FitzHugh-Nagumo Dynamics Test ===\n');

% Generate FHN data (simplified version of demo)
rng(0);
sigma = 0.2;
epsilon = 1/12.5;
a = 0.7;
b = 2;
R = 0.1;
I_ext = R * 3.5;

drift_V = @(V,W) V - V.^3/3 - W + I_ext;
drift_W = @(V,W) epsilon*(V + a - b*W);

dt = 1;
N = 500;  % Smaller for faster comparison
V = zeros(N,1);
W = zeros(N,1);
V(1) = -1.5;
W(1) = 0;

% Heun's method integration
for n = 1:N-1
    k1V = drift_V(V(n), W(n)) * dt;
    k1W = drift_W(V(n), W(n)) * dt;
    
    V_pred = V(n) + k1V;
    W_pred = W(n) + k1W;
    
    k2V = drift_V(V_pred, W_pred) * dt;
    k2W = drift_W(V_pred, W_pred) * dt;
    
    avg_dV = (k1V + k2V)/2;
    avg_dW = (k1W + k2W)/2;
    
    dW_noise = sqrt(dt)*randn;
    
    V(n+1) = V(n) + avg_dV + sigma*dW_noise;
    W(n+1) = W(n) + avg_dW;
end

% Preprocess and create Hankel matrix
synth_data = V - mean(V);
synth_data = synth_data / max(abs(synth_data));
X_fhn = gpuArray(synth_data);
H = mvts2hankel(X_fhn, 20);

fprintf('FHN Hankel matrix: %dx%d\n', size(H, 1), size(H, 2));

% Test both implementations on FHN data
fhn_params = struct('n_neighbors', 5, 'min_dist', 0.1, 'n_epochs', 200);

% Our implementation
tic;
Y_fhn_our = umap_core(H, fhn_params.n_neighbors, fhn_params.min_dist, fhn_params.n_epochs);
time_fhn_our = toc;

% Original implementation (if available)
if original_available
    try
        tic;
        [Y_fhn_original, ~] = run_umap(gather(H), ...
            'n_neighbors', fhn_params.n_neighbors, ...
            'min_dist', fhn_params.min_dist, ...
            'n_epochs', fhn_params.n_epochs, ...
            'verbose', 'none', ...
            'method', 'MEX');
        time_fhn_original = toc;
    catch
        Y_fhn_original = tsne(gather(H), 'NumDimensions', 2, 'Verbose', 0);
        time_fhn_original = NaN;
    end
else
    Y_fhn_original = tsne(gather(H), 'NumDimensions', 2, 'Verbose', 0);
    time_fhn_original = NaN;
end

% Plot FHN results
subplot(2, 3, 4);
plot(1:N, V);
title('FHN Time Series');
xlabel('Time'); ylabel('Voltage');

subplot(2, 3, 5);
scatter(Y_fhn_our(:,1), Y_fhn_our(:,2), 15, 1:size(Y_fhn_our,1), 'filled');
title(sprintf('Our umap\\_core FHN (%.1fs)', time_fhn_our));
xlabel('UMAP 1'); ylabel('UMAP 2');
colorbar;

subplot(2, 3, 6);
scatter(Y_fhn_original(:,1), Y_fhn_original(:,2), 15, 1:size(Y_fhn_original,1), 'filled');
if original_available && ~isnan(time_fhn_original)
    title(sprintf('Original FHN (%.1fs)', time_fhn_original));
else
    title('t-SNE FHN (comparison)');
end
xlabel('Dimension 1'); ylabel('Dimension 2');
colorbar;

sgtitle('UMAP Implementation Comparison', 'FontSize', 16, 'FontWeight', 'bold');

%% Final Summary
fprintf('\n=== Final Comparison Summary ===\n');
fprintf('Swiss Roll Data (%d points):\n', n_points);
fprintf('  Our implementation:      %.2f seconds\n', time_our);
if original_available
    fprintf('  Original implementation: %.2f seconds\n', time_original);
    if ~isnan(time_original)
        fprintf('  Speed ratio:             %.1fx faster\n', time_original / time_our);
    end
end

fprintf('\nFHN Hankel Data (%d points):\n', size(H, 1));
fprintf('  Our implementation:      %.2f seconds\n', time_fhn_our);
if original_available && ~isnan(time_fhn_original)
    fprintf('  Original implementation: %.2f seconds\n', time_fhn_original);
    fprintf('  Speed ratio:             %.1fx faster\n', time_fhn_original / time_fhn_our);
end

fprintf('\nKey advantages of our implementation:\n');
fprintf('  ✓ GPU acceleration with gpuArray\n');
fprintf('  ✓ Vectorized operations (no explicit loops)\n');
fprintf('  ✓ Based on latest umap_v4 algorithm\n');
fprintf('  ✓ Epochs per sample scheduling\n');
fprintf('  ✓ Efficient gradient accumulation\n');
fprintf('  ✓ Built-in Hankel matrix support\n');

fprintf('\nComparison completed successfully!\n');
