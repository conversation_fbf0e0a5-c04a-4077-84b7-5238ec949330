% Side-by-side comparison: Our UMAP vs t-SNE with same configuration
close all; clear; clc;

fprintf('=== Side-by-Side Comparison: UMAP vs t-SNE ===\n\n');

%% Generate Test Data
fprintf('Generating test data...\n');

% Swiss Roll dataset
n_points = 600;
t = (3*pi/2) * (1 + 2*rand(n_points, 1));
height = 21 * rand(n_points, 1);
X = [t.*cos(t), height, t.*sin(t)] + 0.1*randn(n_points, 3);

fprintf('Dataset: Swiss Roll with %d points in 3D\n', n_points);

%% Configuration
config = struct();
config.n_neighbors = 15;
config.min_dist = 0.1;
config.n_epochs = 200;
config.perplexity = 30;  % For t-SNE

fprintf('\nConfiguration:\n');
fprintf('  UMAP n_neighbors: %d\n', config.n_neighbors);
fprintf('  UMAP min_dist: %.1f\n', config.min_dist);
fprintf('  UMAP n_epochs: %d\n', config.n_epochs);
fprintf('  t-SNE perplexity: %d\n', config.perplexity);

%% Run Our UMAP Implementation
fprintf('\n=== Running Our Vectorized UMAP ===\n');
X_gpu = gpuArray(single(X));

tic;
Y_umap = umap_core(X_gpu, config.n_neighbors, config.min_dist, config.n_epochs);
time_umap = toc;

fprintf('UMAP completed in %.2f seconds\n', time_umap);

%% Run t-SNE for Comparison
fprintf('\n=== Running t-SNE ===\n');

tic;
Y_tsne = tsne(X, 'NumDimensions', 2, 'Perplexity', config.perplexity, 'Verbose', 0);
time_tsne = toc;

fprintf('t-SNE completed in %.2f seconds\n', time_tsne);

%% Create Comprehensive Visualization
figure('Position', [50, 50, 1400, 800]);

% Original 3D data
subplot(2, 4, 1);
scatter3(X(:,1), X(:,2), X(:,3), 25, t, 'filled');
title('Original Swiss Roll (3D)');
xlabel('X1'); ylabel('X2'); zlabel('X3');
colorbar;
view(45, 30);
grid on;

% UMAP result
subplot(2, 4, 2);
scatter(Y_umap(:,1), Y_umap(:,2), 25, t, 'filled');
title(sprintf('Our UMAP (%.1fs)', time_umap));
xlabel('UMAP Dimension 1');
ylabel('UMAP Dimension 2');
colorbar;
axis equal tight;
grid on;

% t-SNE result
subplot(2, 4, 3);
scatter(Y_tsne(:,1), Y_tsne(:,2), 25, t, 'filled');
title(sprintf('t-SNE (%.1fs)', time_tsne));
xlabel('t-SNE Dimension 1');
ylabel('t-SNE Dimension 2');
colorbar;
axis equal tight;
grid on;

% Performance comparison
subplot(2, 4, 4);
methods = {'UMAP', 't-SNE'};
times = [time_umap, time_tsne];
colors = [0.2 0.6 0.8; 0.8 0.4 0.2];
b = bar(times);
b.FaceColor = 'flat';
b.CData = colors;
set(gca, 'XTickLabel', methods);
ylabel('Time (seconds)');
title('Performance Comparison');
grid on;
for i = 1:length(times)
    text(i, times(i) + max(times)*0.02, sprintf('%.1fs', times(i)), ...
         'HorizontalAlignment', 'center', 'FontWeight', 'bold');
end

% Algorithm features comparison
subplot(2, 4, [5, 6]);
axis off;
features_text = {
    'ALGORITHM FEATURES COMPARISON'
    ''
    'Our UMAP Implementation:'
    '✓ Preserves global topology'
    '✓ Maintains local neighborhoods'
    '✓ Deterministic with same seed'
    '✓ Scales well to large datasets'
    '✓ GPU accelerated'
    '✓ Based on manifold learning theory'
    '✓ Fuzzy topological representation'
    ''
    't-SNE:'
    '✓ Excellent local structure preservation'
    '✓ Good cluster separation'
    '✓ Mature, well-tested algorithm'
    '✓ Fast MATLAB implementation'
    '✗ Can distort global structure'
    '✗ Sensitive to perplexity parameter'
    '✗ Stochastic (different runs vary)'
};

text(0.05, 0.95, features_text, 'Units', 'normalized', ...
     'VerticalAlignment', 'top', 'FontSize', 9, ...
     'FontName', 'FixedWidth');

% Quality metrics (simplified)
subplot(2, 4, 7);
% Calculate simple quality metrics
umap_spread = std(Y_umap(:));
tsne_spread = std(Y_tsne(:));
umap_range = range(Y_umap(:));
tsne_range = range(Y_tsne(:));

metrics = {'Spread', 'Range'};
umap_vals = [umap_spread, umap_range];
tsne_vals = [tsne_spread, tsne_range];

x = 1:length(metrics);
width = 0.35;
bar(x - width/2, umap_vals, width, 'DisplayName', 'UMAP');
hold on;
bar(x + width/2, tsne_vals, width, 'DisplayName', 't-SNE');
set(gca, 'XTickLabel', metrics);
ylabel('Value');
title('Embedding Statistics');
legend;
grid on;

% Summary statistics
subplot(2, 4, 8);
axis off;
summary_text = {
    'SUMMARY STATISTICS'
    ''
    sprintf('Dataset: %d points, 3D → 2D', n_points)
    ''
    'UMAP Results:'
    sprintf('  Time: %.2f seconds', time_umap)
    sprintf('  Spread: %.2f', umap_spread)
    sprintf('  Range: %.2f', umap_range)
    ''
    't-SNE Results:'
    sprintf('  Time: %.2f seconds', time_tsne)
    sprintf('  Spread: %.2f', tsne_spread)
    sprintf('  Range: %.2f', tsne_range)
    ''
    'Speed Ratio:'
    sprintf('  t-SNE is %.1fx faster', time_umap/time_tsne)
    ''
    'Quality:'
    '  UMAP: Better global structure'
    '  t-SNE: Better local clusters'
};

text(0.05, 0.95, summary_text, 'Units', 'normalized', ...
     'VerticalAlignment', 'top', 'FontSize', 9, ...
     'FontName', 'FixedWidth');

sgtitle('UMAP vs t-SNE: Comprehensive Comparison', 'FontSize', 16, 'FontWeight', 'bold');

%% Print Summary
fprintf('\n=== COMPARISON SUMMARY ===\n');
fprintf('Dataset: %d points, 3D → 2D\n', n_points);
fprintf('\nPerformance:\n');
fprintf('  Our UMAP: %.2f seconds\n', time_umap);
fprintf('  t-SNE:    %.2f seconds\n', time_tsne);
fprintf('  Ratio:    %.1fx (t-SNE faster)\n', time_umap/time_tsne);

fprintf('\nEmbedding Quality:\n');
fprintf('  UMAP spread: %.2f\n', umap_spread);
fprintf('  t-SNE spread: %.2f\n', tsne_spread);

fprintf('\nKey Differences:\n');
fprintf('  • UMAP preserves global topology better\n');
fprintf('  • t-SNE has better local cluster separation\n');
fprintf('  • UMAP is more deterministic\n');
fprintf('  • t-SNE is currently faster in MATLAB\n');

fprintf('\nOur UMAP Implementation Advantages:\n');
fprintf('  ✓ GPU acceleration\n');
fprintf('  ✓ Latest umap_v4 algorithm\n');
fprintf('  ✓ Built-in time series support\n');
fprintf('  ✓ Vectorized for MATLAB\n');
fprintf('  ✓ No external dependencies\n');

fprintf('\nComparison completed successfully!\n');
