% Debug UMAP v4 implementation
close all; clear; clc;

fprintf('=== Debugging UMAP v4 Implementation ===\n');

%% Simple 2D test case
n_points = 100;
t = linspace(0, 2*pi, n_points)';
X = [cos(t), sin(t)] + 0.05*randn(n_points, 2);

fprintf('Input data: %dx%d\n', size(X, 1), size(X, 2));
fprintf('Input range: [%.3f, %.3f]\n', min(X(:)), max(X(:)));

%% Run UMAP with debugging
Y = umap_core_v4(single(X), 5, 0.1, 50, 'verbose', true, 'init', 'random');

fprintf('Output data: %dx%d\n', size(Y, 1), size(Y, 2));
fprintf('Output range: [%.3f, %.3f]\n', min(Y(:)), max(Y(:)));

%% Check if structure is preserved
figure('Position', [100, 100, 800, 400]);

subplot(1, 2, 1);
scatter(X(:,1), X(:,2), 30, t, 'filled');
title('Original Circle');
xlabel('X1'); ylabel('X2');
colorbar;
axis equal tight;

subplot(1, 2, 2);
scatter(Y(:,1), Y(:,2), 30, t, 'filled');
title('UMAP Embedding');
xlabel('UMAP 1'); ylabel('UMAP 2');
colorbar;
axis equal tight;

% Check correlation
corr_1 = abs(corr(t, Y(:,1)));
corr_2 = abs(corr(t, Y(:,2)));
max_corr = max(corr_1, corr_2);

fprintf('Structure preservation correlation: %.3f\n', max_corr);

%% Test with different parameters
fprintf('\n=== Testing different parameters ===\n');

params_to_test = [
    5, 0.01, 50;   % Small min_dist
    5, 0.1, 50;    % Default min_dist  
    5, 0.5, 50;    % Large min_dist
    10, 0.1, 50;   % More neighbors
    15, 0.1, 100;  % Even more neighbors, more epochs
];

correlations = zeros(size(params_to_test, 1), 1);

for i = 1:size(params_to_test, 1)
    n_neigh = params_to_test(i, 1);
    min_d = params_to_test(i, 2);
    n_ep = params_to_test(i, 3);
    
    fprintf('Testing: n_neighbors=%d, min_dist=%.2f, n_epochs=%d\n', n_neigh, min_d, n_ep);
    
    Y_test = umap_core_v4(single(X), n_neigh, min_d, n_ep, 'verbose', false);
    
    corr_1 = abs(corr(t, Y_test(:,1)));
    corr_2 = abs(corr(t, Y_test(:,2)));
    correlations(i) = max(corr_1, corr_2);
    
    fprintf('  Correlation: %.3f\n', correlations(i));
end

[best_corr, best_idx] = max(correlations);
fprintf('\nBest parameters: n_neighbors=%d, min_dist=%.2f, n_epochs=%d (corr=%.3f)\n', ...
    params_to_test(best_idx, 1), params_to_test(best_idx, 2), params_to_test(best_idx, 3), best_corr);

%% Test initialization methods
fprintf('\n=== Testing initialization methods ===\n');

Y_random = umap_core_v4(single(X), 10, 0.1, 50, 'verbose', false, 'init', 'random');
Y_spectral = umap_core_v4(single(X), 10, 0.1, 50, 'verbose', false, 'init', 'spectral');

corr_random = max(abs(corr(t, Y_random(:,1))), abs(corr(t, Y_random(:,2))));
corr_spectral = max(abs(corr(t, Y_spectral(:,1))), abs(corr(t, Y_spectral(:,2))));

fprintf('Random init correlation: %.3f\n', corr_random);
fprintf('Spectral init correlation: %.3f\n', corr_spectral);

figure('Position', [100, 500, 1200, 400]);

subplot(1, 3, 1);
scatter(X(:,1), X(:,2), 30, t, 'filled');
title('Original');
axis equal tight;

subplot(1, 3, 2);
scatter(Y_random(:,1), Y_random(:,2), 30, t, 'filled');
title(sprintf('Random Init (%.3f)', corr_random));
axis equal tight;

subplot(1, 3, 3);
scatter(Y_spectral(:,1), Y_spectral(:,2), 30, t, 'filled');
title(sprintf('Spectral Init (%.3f)', corr_spectral));
axis equal tight;

fprintf('\nDebugging completed!\n');
