function Y = umap_core_v4(X, n_neighbors, min_dist, n_epochs, varargin)
% UMAP_CORE_V4 MATLAB implementation of UMAP v4 algorithm with epochs per sample
%
% Inputs:
%   X - Input data matrix (N x d) or gpuArray
%   n_neighbors - Number of nearest neighbors (default: 15)
%   min_dist - Minimum distance in embedding (default: 0.1)
%   n_epochs - Number of optimization epochs (default: auto)
%
% Optional parameters (name-value pairs):
%   'n_components' - Output dimensions (default: 2)
%   'learning_rate' - Learning rate (default: 1.0)
%   'repulsion_strength' - Repulsion strength (default: 1.0)
%   'negative_sample_rate' - Negative sampling rate (default: 1.0)
%   'init' - Initialization method: 'spectral' or 'random' (default: 'random')
%   'random_state' - Random seed (default: [])
%   'verbose' - Display progress (default: false)
%
% Output:
%   Y - 2D embedding (N x n_components)
%
% This implementation follows the umap_v4 algorithm with:
% - Epochs per sample scheduling for efficient sampling
% - Vectorized operations for MATLAB optimization
% - GPU acceleration support
% - Weight-based edge sampling strategy

% Parse inputs
p = inputParser;
addRequired(p, 'X');
addRequired(p, 'n_neighbors');
addRequired(p, 'min_dist');
addRequired(p, 'n_epochs');
addParameter(p, 'n_components', 2);
addParameter(p, 'learning_rate', 1.0);
addParameter(p, 'repulsion_strength', 1.0);
addParameter(p, 'negative_sample_rate', 1.0);
addParameter(p, 'init', 'random');
addParameter(p, 'random_state', []);
addParameter(p, 'verbose', false);
parse(p, X, n_neighbors, min_dist, n_epochs, varargin{:});

% Extract parameters
n_components = p.Results.n_components;
learning_rate = p.Results.learning_rate;
repulsion_strength = p.Results.repulsion_strength;
negative_sample_rate = p.Results.negative_sample_rate;
init_method = p.Results.init;
random_state = p.Results.random_state;
verbose = p.Results.verbose;

% Set random seed if provided
if ~isempty(random_state)
    rng(random_state);
end

% Ensure single precision for GPU efficiency
if isa(X, 'gpuArray')
    X = single(X);
    use_gpu = true;
else
    X = single(X);
    use_gpu = false;
end

[n_samples, n_features] = size(X);

if verbose
    fprintf('UMAP v4: Processing %d samples with %d features\n', n_samples, n_features);
    fprintf('Parameters: n_neighbors=%d, min_dist=%.3f, n_epochs=%d\n', ...
        n_neighbors, min_dist, n_epochs);
end

%% Step 1: Compute k-nearest neighbors
if verbose, fprintf('Computing k-nearest neighbors...\n'); end

% Use cosine distance like Python implementation
if use_gpu
    X_cpu = gather(X);
    [knn_indices, knn_dists] = knnsearch(X_cpu, X_cpu, 'K', n_neighbors+1, 'Distance', 'cosine');
    % Remove self-connections (first column is always distance 0 to self)
    knn_indices = knn_indices(:, 2:end);
    knn_dists = knn_dists(:, 2:end);

    % Convert to similarities (1 - cosine_distance)
    knn_similarities = 1 - knn_dists;

    % Convert back to GPU
    knn_similarities = gpuArray(single(knn_similarities'));
    knn_indices = gpuArray(int32(knn_indices' - 1)); % Convert to 0-based indexing
else
    [knn_indices, knn_dists] = knnsearch(X, X, 'K', n_neighbors+1, 'Distance', 'cosine');
    % Remove self-connections
    knn_indices = knn_indices(:, 2:end);
    knn_dists = knn_dists(:, 2:end);

    % Convert to similarities (1 - cosine_distance)
    knn_similarities = single(1 - knn_dists');
    knn_indices = int32(knn_indices' - 1); % Convert to 0-based indexing
end

%% Step 2: Build adjacency matrix (following Python to_adjacency_matrix)
if verbose, fprintf('Building adjacency matrix...\n'); end

% Convert k-NN to adjacency matrix following Python implementation
% Python: rows, cols, distances = convert_to_sparse(knn_indices, knn_similarities)
[n_neighbors_actual, n_samples_actual] = size(knn_indices);

% Create edge list (both directions for each edge)
total_edges = n_neighbors_actual * n_samples_actual * 2;
rows = zeros(total_edges, 1, 'like', knn_indices);
cols = zeros(total_edges, 1, 'like', knn_indices);
edge_similarities = zeros(total_edges, 1, 'like', knn_similarities);

edge_count = 0;
for i = 1:n_samples_actual
    for k = 1:n_neighbors_actual
        j = knn_indices(k, i);
        sim = knn_similarities(k, i);

        % Add edge (i-1, j) - convert to 0-based for consistency
        edge_count = edge_count + 1;
        rows(edge_count) = i - 1;  % 0-based
        cols(edge_count) = j;      % Already 0-based
        edge_similarities(edge_count) = sim;

        % Add reverse edge (j, i-1)
        edge_count = edge_count + 1;
        rows(edge_count) = j;      % Already 0-based
        cols(edge_count) = i - 1;  % 0-based
        edge_similarities(edge_count) = sim;
    end
end

% Trim to actual size
rows = rows(1:edge_count);
cols = cols(1:edge_count);
edge_similarities = edge_similarities(1:edge_count);

% Sort edges and aggregate (following Python aggregate_edges)
if use_gpu
    rows_cpu = gather(rows);
    cols_cpu = gather(cols);
    edge_similarities_cpu = gather(edge_similarities);
else
    rows_cpu = rows;
    cols_cpu = cols;
    edge_similarities_cpu = edge_similarities;
end

% Sort by (row, col)
[~, sort_idx] = sortrows([double(rows_cpu), double(cols_cpu)]);
rows_sorted = rows_cpu(sort_idx);
cols_sorted = cols_cpu(sort_idx);
edge_similarities_sorted = edge_similarities_cpu(sort_idx);

% Aggregate duplicate edges (take maximum similarity)
[unique_edges, ~, idx] = unique([double(rows_sorted), double(cols_sorted)], 'rows');
final_similarities = accumarray(idx, edge_similarities_sorted, [], @max);

final_rows = unique_edges(:, 1);
final_cols = unique_edges(:, 2);

% Create sparse adjacency matrix
if use_gpu
    % Convert back to GPU
    final_rows = gpuArray(int32(final_rows));
    final_cols = gpuArray(int32(final_cols));
    final_similarities = gpuArray(single(final_similarities));

    % For GPU, create dense matrix
    A_sym = zeros(n_samples, n_samples, 'like', X);
    linear_idx = sub2ind([n_samples, n_samples], final_rows + 1, final_cols + 1);
    A_sym(linear_idx) = final_similarities;
else
    A_sym = sparse(final_rows + 1, final_cols + 1, double(final_similarities), n_samples, n_samples);
end

%% Step 3: Extract edges for optimization
if use_gpu
    [edge_rows, edge_cols] = find(A_sym > 0);
    edge_weights = A_sym(A_sym > 0);
else
    [edge_rows, edge_cols, edge_weights] = find(A_sym);
end

% Convert to 0-based indexing to match Python
edge_rows = edge_rows - 1;
edge_cols = edge_cols - 1;

n_edges = length(edge_rows);
if verbose, fprintf('Found %d edges for optimization\n', n_edges); end

%% Step 4: Initialize embedding
if verbose, fprintf('Initializing embedding...\n'); end

if strcmp(init_method, 'spectral')
    % Simplified spectral initialization
    if use_gpu
        % For GPU, use random initialization (spectral is complex on GPU)
        % Y = 10 * (rand(n_samples, n_components, 'like', X) - 0.5);
        [V, ~] = eigs(gather(A_sym), n_components+1, 'largestabs');
        Y = 10 * V(:, 2:end); % Skip first eigenvector
    else
        try
            [V, ~] = eigs(A_sym, n_components+1, 'largestabs');
            Y = 10 * V(:, 2:end); % Skip first eigenvector
        catch
            Y = 10 * (rand(n_samples, n_components) - 0.5);
        end
        Y = single(Y);
        if use_gpu, Y = gpuArray(Y); end
    end
else
    % Random initialization
    Y = 10 * (rand(n_samples, n_components, 'like', X) - 0.5);
end

%% Step 5: Compute epochs per sample (umap_v4 key feature)
if verbose, fprintf('Computing epochs per sample schedule...\n'); end

% Get minimum similarity scores for weight computation (following Python)
% Python: knn_min_scores = knn_similarities.min(-1)
knn_min_scores = min(knn_similarities, [], 1)';  % Min along neighbors dimension
if use_gpu, knn_min_scores = gpuArray(single(knn_min_scores)); end

% Python: weights = 1 / knn_min_scores[graph.row] ** 2
% Convert edge_rows from 0-based to 1-based for MATLAB indexing
edge_rows_1based = edge_rows + 1;
weights = 1 ./ (knn_min_scores(edge_rows_1based) .^ 2);

% Compute epochs per sample
epochs_per_sample = make_epochs_per_sample(weights, n_epochs);
epoch_of_next_sample = epochs_per_sample;

%% Step 6: UMAP optimization with epochs per sample
if verbose, fprintf('Starting UMAP optimization...\n'); end

% UMAP parameters - compute a and b from min_dist
spread = 1.0;  % Default spread parameter
try
    [a, b] = find_ab_params(spread, min_dist);
catch
    % If find_ab_params not available, use our own implementation
    [a, b] = find_ab_params_local(spread, min_dist);
end
a = single(a);
b = single(b);
b1 = single(b - 1.0);
a1 = single(0.001);
c0 = single(-2.0 * a * b);
c1 = single(2.0 * repulsion_strength * b);

% Progress tracking
if verbose
    progress_step = max(1, floor(n_epochs / 20));
end

for epoch = 1:n_epochs
    % Compute learning rate
    lr = single(learning_rate * (1 - epoch / n_epochs));

    % Find edges to update in this epoch
    edges_to_update = epoch_of_next_sample <= epoch;

    if any(edges_to_update)
        % Get active edges
        active_rows = edge_rows(edges_to_update);
        active_cols = edge_cols(edges_to_update);
        active_weights = edge_weights(edges_to_update);

        % Vectorized attraction force computation
        Y = update_attraction_forces_vectorized(Y, active_rows, active_cols, ...
            active_weights, lr, a, b, b1, c0);

        % Update epoch schedule
        epoch_of_next_sample(edges_to_update) = ...
            epoch_of_next_sample(edges_to_update) + epochs_per_sample(edges_to_update);

        % Negative sampling (following Python implementation)
        n_neg_samples = round(negative_sample_rate);
        if n_neg_samples > 0
            % Python samples negatives for each unique row that was updated
            unique_rows = unique(active_rows);
            Y = update_repulsion_forces_vectorized(Y, unique_rows, ...
                n_neg_samples, lr, a, b, a1, c1, n_samples);
        end
    end

    % Progress reporting
    if verbose && mod(epoch, progress_step) == 0
        fprintf('Epoch %d/%d (%.1f%%)\n', epoch, n_epochs, 100*epoch/n_epochs);
    end
end

if verbose, fprintf('UMAP v4 optimization completed!\n'); end

% Convert back to CPU if needed
if use_gpu && nargout > 0
    Y = gather(Y);
end

end

%% Helper Functions

function sigma = find_sigma(distances, rho, target)
% Find sigma value for local connectivity using binary search
    sigma_min = 1e-5;
    sigma_max = 1e5;

    for iter = 1:50  % Binary search iterations
        sigma = (sigma_min + sigma_max) / 2;

        % Compute perplexity with current sigma
        psum = 0;
        for j = 1:length(distances)
            if distances(j) > rho
                psum = psum + exp(-(distances(j) - rho) / sigma);
            else
                psum = psum + 1;
            end
        end

        if psum > 0
            perplexity = log2(psum);
        else
            perplexity = 0;
        end

        if abs(perplexity - target) < 1e-5
            break;
        elseif perplexity > target
            sigma_max = sigma;
        else
            sigma_min = sigma;
        end
    end
end

function sigma = find_sigma_fast(distances, rho, target)
% Optimized sigma finding with vectorized operations
    sigma_min = 1e-5;
    sigma_max = 1e5;

    % Precompute mask for distances > rho
    mask = distances > rho;
    dist_diff = distances - rho;

    for iter = 1:20  % Fewer iterations for speed
        sigma = (sigma_min + sigma_max) / 2;

        % Vectorized perplexity computation
        exp_vals = exp(-dist_diff(mask) / sigma);
        psum = sum(mask) + sum(exp_vals); % Count of distances <= rho + sum of exp values

        if psum > 0
            perplexity = log2(psum);
        else
            perplexity = 0;
        end

        if abs(perplexity - target) < 1e-4  % Slightly relaxed tolerance
            break;
        elseif perplexity > target
            sigma_max = sigma;
        else
            sigma_min = sigma;
        end
    end
end

function epochs_per_sample = make_epochs_per_sample(weights, n_epochs)
% Compute epochs per sample based on edge weights (umap_v4 feature)
    min_value = max(weights) / n_epochs;
    weights(weights < min_value) = min_value;

    n_samples = n_epochs * (weights / max(weights));
    epochs_per_sample = -ones(size(weights), 'like', weights);

    valid_mask = n_samples > 0;
    epochs_per_sample(valid_mask) = n_epochs ./ n_samples(valid_mask);
end

function Y = update_attraction_forces_vectorized(Y, rows, cols, scores, lr, a, b, b1, c0)
% Vectorized attraction force update following umap_v4 algorithm
    if isempty(rows)
        return;
    end

    % Get point coordinates (convert from 0-based to 1-based indexing)
    xi = Y(rows + 1, :);  % N_edges x n_components
    xj = Y(cols + 1, :);  % N_edges x n_components

    % Compute differences and distances
    diff = xi - xj;  % N_edges x n_components
    dij_sq = sum(diff.^2, 2);  % N_edges x 1

    % Avoid division by zero
    valid_mask = dij_sq > 0;
    if ~any(valid_mask)
        return;
    end

    % Apply mask to valid edges only
    diff_valid = diff(valid_mask, :);
    dij_sq_valid = dij_sq(valid_mask);
    scores_valid = scores(valid_mask);
    rows_valid = rows(valid_mask) + 1;  % Convert to 1-based
    cols_valid = cols(valid_mask) + 1;  % Convert to 1-based

    % Compute gradient coefficient (vectorized)
    % Python uses dij = core.rdot(diff) which is SQUARED distance
    % grad_coeff = c0 * (dij**b1) / (a * (dij**b) + one)
    grad_coeff = c0 * (dij_sq_valid.^b1) ./ (a * (dij_sq_valid.^b) + 1);

    % Compute gradients
    grad = scores_valid .* grad_coeff .* diff_valid;  % N_valid x n_components

    % Clip gradients
    grad = max(-2, min(2, grad)) * lr;

    % Apply gradients using accumarray for vectorized updates
    n_samples = size(Y, 1);
    n_components = size(Y, 2);

    % More efficient gradient application
    if n_components == 2  % Common case optimization
        Y(:, 1) = Y(:, 1) + accumarray(rows_valid, grad(:, 1), [n_samples, 1]) ...
                          - accumarray(cols_valid, grad(:, 1), [n_samples, 1]);
        Y(:, 2) = Y(:, 2) + accumarray(rows_valid, grad(:, 2), [n_samples, 1]) ...
                          - accumarray(cols_valid, grad(:, 2), [n_samples, 1]);
    else
        for d = 1:n_components
            % Positive gradients for rows
            Y(:, d) = Y(:, d) + accumarray(rows_valid, grad(:, d), [n_samples, 1]);
            % Negative gradients for cols
            Y(:, d) = Y(:, d) - accumarray(cols_valid, grad(:, d), [n_samples, 1]);
        end
    end
end

function Y = update_repulsion_forces_vectorized(Y, rows, n_neg_samples, lr, a, b, a1, c1, n_samples)
% Vectorized repulsion force update with negative sampling
    if isempty(rows) || n_neg_samples == 0
        return;
    end

    n_active = length(rows);
    n_components = size(Y, 2);

    % Generate random negative samples (0-based like Python)
    neg_indices = randi(n_samples, n_active * n_neg_samples, 1, 'like', rows) - 1;

    % Expand rows to match negative samples
    expanded_rows = repelem(rows, n_neg_samples);

    % Remove self-connections
    valid_mask = expanded_rows ~= neg_indices;
    expanded_rows = expanded_rows(valid_mask);
    neg_indices = neg_indices(valid_mask);

    if isempty(expanded_rows)
        return;
    end

    % Get coordinates (convert to 1-based indexing)
    xi = Y(expanded_rows + 1, :);
    xj = Y(neg_indices + 1, :);

    % Compute differences and distances
    diff = xi - xj;
    dij_sq = sum(diff.^2, 2);

    % Avoid division by zero
    valid_mask = dij_sq > 0;
    if ~any(valid_mask)
        return;
    end

    diff_valid = diff(valid_mask, :);
    dij_sq_valid = dij_sq(valid_mask);
    rows_valid = expanded_rows(valid_mask) + 1;  % Convert to 1-based

    % Compute repulsion gradient coefficient
    % Python: grad_coeff = c1 / ((a1 + dij) * (a * (dij**b) + one))
    % where dij is SQUARED distance
    grad_coeff = c1 ./ ((a1 + dij_sq_valid) .* (a * (dij_sq_valid.^b) + 1));

    % Compute gradients
    grad = grad_coeff .* diff_valid;

    % Clip gradients
    grad = max(-2, min(2, grad)) * lr;

    % Apply gradients (only positive for repulsion)
    if n_components == 2  % Common case optimization
        Y(:, 1) = Y(:, 1) + accumarray(rows_valid, grad(:, 1), [n_samples, 1]);
        Y(:, 2) = Y(:, 2) + accumarray(rows_valid, grad(:, 2), [n_samples, 1]);
    else
        for d = 1:n_components
            Y(:, d) = Y(:, d) + accumarray(rows_valid, grad(:, d), [n_samples, 1]);
        end
    end
end

function [a, b] = find_ab_params_local(spread, min_dist)
% Local implementation of find_ab_params
    curve = @(params, x) 1 ./ (1 + params(1) * x.^(2 * params(2)));

    xv = linspace(0, 3*spread, 300);
    yv = (xv < min_dist) + (xv >= min_dist) .* exp(-(xv - min_dist) / spread);

    % Use fminsearch to fit the curve
    err = @(params) sum((yv - curve(params, xv)).^2);
    params = fminsearch(err, [1, 1]);

    a = params(1);
    b = params(2);
end