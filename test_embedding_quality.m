% Test embedding quality of UMAP v4 implementation
close all; clear; clc;

fprintf('=== Testing UMAP v4 Embedding Quality ===\n');

%% Test 1: Simple 2D spiral (should preserve spiral structure)
fprintf('\nTest 1: 2D Spiral Structure Preservation\n');
n_points = 300;
t = linspace(0, 4*pi, n_points)';
X_spiral = [t.*cos(t), t.*sin(t)] + 0.05*randn(n_points, 2);

% Run UMAP
Y_spiral = umap_core_v4(single(X_spiral), 10, 0.1, 150, 'verbose', false);

% Check if spiral structure is preserved
% Compute correlation between original parameter t and UMAP coordinates
corr_x = abs(corr(t, Y_spiral(:,1)));
corr_y = abs(corr(t, Y_spiral(:,2)));
max_corr_spiral = max(corr_x, corr_y);

fprintf('Spiral structure preservation: %.3f (>0.7 is good)\n', max_corr_spiral);

%% Test 2: Swiss Roll (3D -> 2D manifold unrolling)
fprintf('\nTest 2: Swiss Roll Manifold Unrolling\n');
n_points = 400;
t = (3*pi/2) * (1 + 2*rand(n_points, 1));
height = 21 * rand(n_points, 1);
X_swiss = [t.*cos(t), height, t.*sin(t)] + 0.1*randn(n_points, 3);

% Run UMAP
Y_swiss = umap_core_v4(single(X_swiss), 15, 0.1, 200, 'verbose', false);

% Check if the intrinsic 2D structure is recovered
% The Swiss roll should unfold to show correlation with t and height
corr_t1 = abs(corr(t, Y_swiss(:,1)));
corr_t2 = abs(corr(t, Y_swiss(:,2)));
corr_h1 = abs(corr(height, Y_swiss(:,1)));
corr_h2 = abs(corr(height, Y_swiss(:,2)));

max_corr_t = max(corr_t1, corr_t2);
max_corr_h = max(corr_h1, corr_h2);

fprintf('Swiss roll t-parameter preservation: %.3f (>0.6 is good)\n', max_corr_t);
fprintf('Swiss roll height preservation: %.3f (>0.6 is good)\n', max_corr_h);

%% Test 3: Clusters (should separate distinct groups)
fprintf('\nTest 3: Cluster Separation\n');
n_per_cluster = 100;
% Create 3 well-separated clusters in 5D
cluster1 = randn(n_per_cluster, 5) + [5, 0, 0, 0, 0];
cluster2 = randn(n_per_cluster, 5) + [0, 5, 0, 0, 0];
cluster3 = randn(n_per_cluster, 5) + [0, 0, 5, 0, 0];

X_clusters = [cluster1; cluster2; cluster3];
labels = [ones(n_per_cluster,1); 2*ones(n_per_cluster,1); 3*ones(n_per_cluster,1)];

% Run UMAP
Y_clusters = umap_core_v4(single(X_clusters), 10, 0.1, 150, 'verbose', false);

% Compute silhouette score to measure cluster separation
try
    silhouette_score = mean(silhouette(Y_clusters, labels));
    fprintf('Cluster separation (silhouette): %.3f (>0.5 is good)\n', silhouette_score);
catch
    % If silhouette function not available, compute simple separation metric
    centers = [mean(Y_clusters(labels==1,:)); mean(Y_clusters(labels==2,:)); mean(Y_clusters(labels==3,:))];
    inter_dist = min(pdist(centers));
    intra_dist = mean([std(Y_clusters(labels==1,:)); std(Y_clusters(labels==2,:)); std(Y_clusters(labels==3,:))]);
    separation = inter_dist / mean(intra_dist);
    fprintf('Cluster separation (distance ratio): %.3f (>2.0 is good)\n', separation);
end

%% Visualization
figure('Position', [100, 100, 1200, 400]);

% Spiral
subplot(1, 3, 1);
scatter(Y_spiral(:,1), Y_spiral(:,2), 20, t, 'filled');
title(sprintf('Spiral (corr=%.2f)', max_corr_spiral));
xlabel('UMAP 1'); ylabel('UMAP 2');
colorbar;
axis equal tight;

% Swiss roll
subplot(1, 3, 2);
scatter(Y_swiss(:,1), Y_swiss(:,2), 20, t, 'filled');
title(sprintf('Swiss Roll (t=%.2f, h=%.2f)', max_corr_t, max_corr_h));
xlabel('UMAP 1'); ylabel('UMAP 2');
colorbar;
axis equal tight;

% Clusters
subplot(1, 3, 3);
colors = [1 0 0; 0 1 0; 0 0 1];
for i = 1:3
    idx = labels == i;
    scatter(Y_clusters(idx,1), Y_clusters(idx,2), 20, colors(i,:), 'filled');
    hold on;
end
title('Clusters');
xlabel('UMAP 1'); ylabel('UMAP 2');
axis equal tight;
legend('Cluster 1', 'Cluster 2', 'Cluster 3');

sgtitle('UMAP v4 Embedding Quality Tests', 'FontSize', 14, 'FontWeight', 'bold');

%% Summary
fprintf('\n=== Quality Assessment Summary ===\n');
fprintf('Spiral preservation:     ');
if max_corr_spiral > 0.7
    fprintf('GOOD (%.3f)\n', max_corr_spiral);
else
    fprintf('POOR (%.3f)\n', max_corr_spiral);
end

fprintf('Swiss roll unfolding:    ');
if max_corr_t > 0.6 && max_corr_h > 0.6
    fprintf('GOOD (t=%.3f, h=%.3f)\n', max_corr_t, max_corr_h);
elseif max_corr_t > 0.4 || max_corr_h > 0.4
    fprintf('FAIR (t=%.3f, h=%.3f)\n', max_corr_t, max_corr_h);
else
    fprintf('POOR (t=%.3f, h=%.3f)\n', max_corr_t, max_corr_h);
end

fprintf('Cluster separation:      ');
if exist('silhouette_score', 'var')
    if silhouette_score > 0.5
        fprintf('GOOD (%.3f)\n', silhouette_score);
    else
        fprintf('POOR (%.3f)\n', silhouette_score);
    end
else
    if separation > 2.0
        fprintf('GOOD (%.3f)\n', separation);
    else
        fprintf('POOR (%.3f)\n', separation);
    end
end

fprintf('\nEmbedding quality test completed!\n');
