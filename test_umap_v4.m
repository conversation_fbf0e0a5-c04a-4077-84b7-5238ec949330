% Quick test of UMAP v4 implementation
close all; clear; clc;

fprintf('=== Testing UMAP v4 Implementation ===\n');

%% Test 1: Small spiral dataset
fprintf('\nTest 1: Small spiral dataset (200 points)\n');
n_points = 200;
t = linspace(0, 4*pi, n_points)';
X_spiral = [t.*cos(t), t.*sin(t)] + 0.05*randn(n_points, 2);

% Test on CPU
fprintf('Testing on CPU...\n');
tic;
Y_cpu = umap_core_v4(single(X_spiral), 10, 0.1, 100, 'verbose', false);
time_cpu = toc;
fprintf('CPU time: %.2f seconds\n', time_cpu);

% Test on GPU (if available)
if gpuDeviceCount > 0
    fprintf('Testing on GPU...\n');
    X_gpu = gpuArray(single(X_spiral));
    tic;
    Y_gpu = umap_core_v4(X_gpu, 10, 0.1, 100, 'verbose', false);
    time_gpu = toc;
    fprintf('GPU time: %.2f seconds\n', time_gpu);
    fprintf('GPU speedup: %.1fx\n', time_cpu / time_gpu);

    % Check results are similar
    Y_gpu_cpu = gather(Y_gpu);
    diff = norm(Y_cpu - Y_gpu_cpu, 'fro');
    fprintf('Difference between CPU and GPU results: %.6f\n', diff);
else
    fprintf('No GPU available, skipping GPU test\n');
    Y_gpu_cpu = Y_cpu;
end

%% Test 2: Swiss roll dataset
fprintf('\nTest 2: Swiss roll dataset (300 points)\n');
n_points = 300;
t = (3*pi/2) * (1 + 2*rand(n_points, 1));
height = 21 * rand(n_points, 1);
X_swiss = [t.*cos(t), height, t.*sin(t)] + 0.1*randn(n_points, 3);

tic;
Y_swiss = umap_core_v4(gpuArray(single(X_swiss)), 15, 0.1, 150, 'verbose', false);
time_swiss = toc;
fprintf('Swiss roll embedding time: %.2f seconds\n', time_swiss);

%% Test 3: Time series (Hankel matrix)
fprintf('\nTest 3: Time series with Hankel matrix\n');
N = 100;
t_ts = linspace(0, 10, N)';
ts_data = sin(t_ts) + 0.3*sin(3*t_ts) + 0.1*randn(N, 1);

% Create Hankel matrix
H = mvts2hankel(gpuArray(single(ts_data)), 15);
fprintf('Hankel matrix size: %dx%d\n', size(H, 1), size(H, 2));

tic;
Y_hankel = umap_core_v4(H, 5, 0.1, 100, 'verbose', false);
time_hankel = toc;
fprintf('Hankel embedding time: %.2f seconds\n', time_hankel);

%% Visualization
figure('Position', [100, 100, 1200, 400]);

% Spiral results
subplot(1, 3, 1);
scatter(Y_cpu(:,1), Y_cpu(:,2), 20, t(1:size(Y_cpu,1)), 'filled');
title('Spiral UMAP v4');
xlabel('UMAP 1'); ylabel('UMAP 2');
colorbar;
axis equal tight;

% Swiss roll results
subplot(1, 3, 2);
Y_swiss_cpu = gather(Y_swiss);
t_swiss = (3*pi/2) * (1 + 2*rand(size(Y_swiss_cpu,1), 1)); % Recreate color vector
scatter(Y_swiss_cpu(:,1), Y_swiss_cpu(:,2), 20, t_swiss, 'filled');
title('Swiss Roll UMAP v4');
xlabel('UMAP 1'); ylabel('UMAP 2');
colorbar;
axis equal tight;

% Hankel results
subplot(1, 3, 3);
Y_hankel_cpu = gather(Y_hankel);
scatter(Y_hankel_cpu(:,1), Y_hankel_cpu(:,2), 15, 1:size(Y_hankel_cpu,1), 'filled');
title('Time Series UMAP v4');
xlabel('UMAP 1'); ylabel('UMAP 2');
colorbar;
axis equal tight;

sgtitle('UMAP v4 Test Results', 'FontSize', 14, 'FontWeight', 'bold');

%% Summary
fprintf('\n=== Test Summary ===\n');
fprintf('Spiral (200 pts):     %.2f seconds\n', time_cpu);
fprintf('Swiss roll (300 pts): %.2f seconds\n', time_swiss);
fprintf('Hankel (%d pts):      %.2f seconds\n', size(H,1), time_hankel);
fprintf('\nAll tests completed successfully!\n');

% Performance scaling estimate
fprintf('\nPerformance scaling estimates:\n');
fprintf('~%.1f seconds per 1000 points\n', time_swiss * (1000/300));
